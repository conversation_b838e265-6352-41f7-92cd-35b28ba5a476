import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSidenavModule } from '@angular/material/sidenav';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { DependentInformations } from '../../models';
import { PlanSummary } from 'src/app/pages/settings/pages/plan/models';
import { PlanSummaryService } from 'src/app/pages/settings/pages/plan/services';
import { AddScheduleFormGroup } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { Router } from '@angular/router';
import moment from 'moment';
import { CustomerOrdersRes } from 'src/app/pages/shop/models';
import { EnsembleClassScheduleSummaryInfo } from 'src/app/pages/schedule-classes/pages/ensemble-class/models';

const DEPENDENCIES = {
  MODULES: [MatButtonModule, SharedModule, CommonModule, MatIconModule, MatSidenavModule]
};

@Component({
  selector: 'app-plan-assigned-success',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES],
  templateUrl: './plan-assigned-success.component.html',
  styleUrls: ['../continue-to-checkout/continue-to-checkout.component.scss', './plan-assigned-success.component.scss']
})
export class PlanAssignedSuccessComponent extends BaseComponent {
  @Input() selectedStudentDetails!: DependentInformations | undefined;
  @Input() selectedStudentPlan!: PlanSummary | undefined;
  @Input() selectedInstrumentName!: string;
  @Input() bookPlanForm!: FormGroup<AddScheduleFormGroup>;
  @Input() shoppingCart!: Array<CustomerOrdersRes>;
  @Input() totalAmount!: number;
  @Input() selectedEnsembleClass!: EnsembleClassScheduleSummaryInfo;

  @Output() closeSideNav = new EventEmitter<void>();

  constructor(protected readonly planSummaryService: PlanSummaryService, private readonly router: Router) {
    super();
  }

  ngOnChanges(): void {
    console.log(this.selectedStudentPlan, 'selectedStudentPlan');
  }

  get getTotalPayableAmount(): number {
    return this.selectedStudentPlan?.isRecurringDiscount ? (this.selectedStudentPlan?.planPrice - this.selectedStudentPlan?.discountedAmount) : this.selectedStudentPlan?.planPrice || 0;
  }

  closeBookAssignedPlan(): void {
    this.closeSideNav.emit();
  }

  onViewSchedule(): void {
    const scheduleDate = moment(this.bookPlanForm.controls.scheduleDate.value);
    const firstDayOfSchedule = this.bookPlanForm.getRawValue().daysOfSchedule[0];
    const actualDate = this.calculateActualScheduleDate(scheduleDate, firstDayOfSchedule);
    this.router.navigate(['/schedule'], { queryParams: { date: actualDate } });
  }

  calculateActualScheduleDate(scheduleDate: moment.Moment, firstDayOfSchedule: number): string {
    const currentDay = moment().isoWeekday();
    const daysToAdd = currentDay === firstDayOfSchedule ? 0 : (7 + firstDayOfSchedule - currentDay) % 7 || 7;
    return scheduleDate.add(daysToAdd, 'days').format(this.constants.dateFormats.yyyy_MM_DD);
  }
}
