import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { AuthService } from 'src/app/auth/services';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { CBGetResponse } from 'src/app/shared/models';
import { AllCustomerCards, AllCardsOfUser, AllAchOfUser } from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { TransactionTypes } from 'src/app/pages/shop/models';
import { SharedModule } from 'src/app/shared/shared.module';
import { ContentLoaderComponent } from 'src/app/shared/components/content-loader/content-loader.component';
import { CurrentMonthPayment, FuturePayment, PlanBillingDetails, RecurringPaymentSchedule, SignedDocumentsInfo } from '../../models';
import { Router } from '@angular/router';

// Interface for plan details
export interface PlanDetails {
  planName: string;
  classType: string;
  visitsPerWeek: number;
  instrument: string;
  startDate: string;
  startTime: string;
  planAmount: number;
}

const DEPENDENCIES = {
  MODULES: [CommonModule, MatIconModule, MatButtonModule, SharedModule],
  COMPONENTS: []
};

@Component({
  selector: 'app-plan-recurring-payment',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './plan-recurring-payment.component.html',
  styleUrl: './plan-recurring-payment.component.scss'
})
export class PlanRecurringPaymentComponent extends BaseComponent implements OnInit {
  @Input() selectedPlanDetail!: SignedDocumentsInfo | null;

  allCustomerCards!: AllCustomerCards;
  defaultCard: AllCardsOfUser | null = null;
  defaultAch: AllAchOfUser | null = null;
  defaultPaymentMethod: 'card' | 'ach' | null = null;
  currentMonthPayment!: number;

  // Plan details data
  planDetails: PlanDetails = {
    planName: 'Weekly Music Lessons (BASIC 1x30)',
    classType: 'Recurring Plan',
    visitsPerWeek: 1,
    instrument: 'Piano',
    startDate: '2025-06-18',
    startTime: '3:00 PM - 3:30 PM',
    planAmount: 150
  };

  constructor(
    private readonly paymentService: PaymentService,
    private readonly authService: AuthService,
    private readonly router: Router,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCurrentUser();
    this.getCurrentMonthPayment();
  }

  getCurrentUser(): void {
    this.showPageLoader = true;
    this.authService
      .getCurrentUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res;
          this.getAllCustomerCards();
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  getAllCustomerCards(): void {
    this.showPageLoader = false;
    this.paymentService
      .getList<CBGetResponse<AllCustomerCards>>(`${API_URL.payment.getAllCustomerCards}?userId=${this.currentUser?.userId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<AllCustomerCards>) => {
          this.allCustomerCards = res.result;
          this.setDefaultPaymentMethod();
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  setDefaultPaymentMethod(): void {
    if (this.allCustomerCards) {
      this.defaultCard = this.allCustomerCards.getAllCardsOfUser?.find(card => card.isDefault) || null;

      this.defaultAch = this.allCustomerCards.getAllAchDetailsOfUser?.find(ach => ach.isDefault) || null;

      if (this.defaultCard) {
        this.defaultPaymentMethod = 'card';
      } else if (this.defaultAch) {
        this.defaultPaymentMethod = 'ach';
      } else {
        this.defaultPaymentMethod = null;
      }
    }
  }

  get calculateTotalAmount(): number {
    if (this.selectedPlanDetail) {
      return (
        this.selectedPlanDetail.planAmount +
        this.selectedPlanDetail.serviceFees +
        this.selectedPlanDetail.registrationFees -
        this.selectedPlanDetail.discountAmount
      );
    }
    return 0;
  }

  getCurrentMonthPayment(): void {
    this.showPageLoader = true;
    this.paymentService
      .add({ startDate: '', daysOfSchedule: [], paidAmount: 0 }, API_URL.payment.currentMonthPaymentCalculationForRecurringPlan)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<number>) => {
          this.currentMonthPayment = res?.result;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  navigateToBillingSection(): void {
    this.router.navigate([this.path.billing.root], {
      queryParams: {
        activeTab: 'Payment Method'
      }
    });
  }

  getPaymentMethodIcon(): string {
    if (this.defaultPaymentMethod === 'card') {
      return 'credit_card';
    } else if (this.defaultPaymentMethod === 'ach') {
      return 'account_balance';
    }
    return 'payment';
  }

  getPaymentMethodDisplay(): string {
    if (this.defaultPaymentMethod === 'card' && this.defaultCard) {
      return `${this.defaultCard.ccType.toUpperCase()} **** ${this.defaultCard.ccNum}`;
    } else if (this.defaultPaymentMethod === 'ach' && this.defaultAch) {
      return `${this.defaultAch.accountType.toUpperCase()} **** ${this.defaultAch.accountNumber.slice(-4)}`;
    }
    return 'No default payment method';
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
}
