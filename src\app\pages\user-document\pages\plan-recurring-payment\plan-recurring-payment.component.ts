import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { AuthService } from 'src/app/auth/services';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { CBGetResponse } from 'src/app/shared/models';
import { AllCustomerCards, AllCardsOfUser, AllAchOfUser } from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { TransactionTypes } from 'src/app/pages/shop/models';
import { SharedModule } from 'src/app/shared/shared.module';
import { ContentLoaderComponent } from 'src/app/shared/components/content-loader/content-loader.component';
import { CurrentMonthPayment, FuturePayment, PlanBillingDetails, RecurringPaymentSchedule, SignedDocumentsInfo } from '../../models';
import { Router } from '@angular/router';

const DEPENDENCIES = {
  MODULES: [CommonModule, MatIconModule, MatButtonModule, SharedModule],
  COMPONENTS: []
};

@Component({
  selector: 'app-plan-recurring-payment',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './plan-recurring-payment.component.html',
  styleUrl: './plan-recurring-payment.component.scss'
})
export class PlanRecurringPaymentComponent extends BaseComponent implements OnInit {
  @Input() selectedPlanDetail!: SignedDocumentsInfo | null;

  allCustomerCards!: AllCustomerCards;
  defaultCard: AllCardsOfUser | null = null;
  defaultAch: AllAchOfUser | null = null;
  defaultPaymentMethod: 'card' | 'ach' | null = null;
  currentMonthPayment!: number;

  constructor(
    private readonly paymentService: PaymentService,
    private readonly authService: AuthService,
    private readonly router: Router,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCurrentUser();
    this.getCurrentMonthPayment();
  }

  getCurrentUser(): void {
    this.showPageLoader = true;
    this.authService
      .getCurrentUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res;
          this.getAllCustomerCards();
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  getAllCustomerCards(): void {
    this.showPageLoader = false;
    this.paymentService
      .getList<CBGetResponse<AllCustomerCards>>(`${API_URL.payment.getAllCustomerCards}?userId=${this.currentUser?.userId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<AllCustomerCards>) => {
          this.allCustomerCards = res.result;
          this.setDefaultPaymentMethod();
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  setDefaultPaymentMethod(): void {
    if (this.allCustomerCards) {
      this.defaultCard = this.allCustomerCards.getAllCardsOfUser?.find(card => card.isDefault) || null;

      this.defaultAch = this.allCustomerCards.getAllAchDetailsOfUser?.find(ach => ach.isDefault) || null;

      if (this.defaultCard) {
        this.defaultPaymentMethod = 'card';
      } else if (this.defaultAch) {
        this.defaultPaymentMethod = 'ach';
      } else {
        this.defaultPaymentMethod = null;
      }
    }
  }

  get calculateTotalAmount(): number {
    if (this.selectedPlanDetail) {
      return (
        this.selectedPlanDetail.planAmount +
        this.selectedPlanDetail.serviceFees +
        this.selectedPlanDetail.registrationFees -
        this.selectedPlanDetail.discountAmount
      );
    }
    return 0;
  }

  getCurrentMonthPayment(): void {
    this.showPageLoader = true;
    this.paymentService
      .add({ startDate: '', daysOfSchedule: [], paidAmount: 0 }, API_URL.payment.currentMonthPaymentCalculationForRecurringPlan)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<number>) => {
          this.currentMonthPayment = res?.result;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  navigateToBillingSection(): void {
    this.router.navigate([this.path.billing.root], {
      queryParams: {
        activeTab: 'Payment Method'
      }
    });
  }

  getPaymentMethodIcon(): string {
    if (this.defaultPaymentMethod === 'card') {
      return 'credit_card';
    } else if (this.defaultPaymentMethod === 'ach') {
      return 'account_balance';
    }
    return 'payment';
  }
}
