import { Injectable } from '@angular/core';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { BaseCrudService } from 'src/app/shared/services';
import { ClassTypes, LessonTypes } from '../models';
import { Constants } from 'src/app/shared/constants';
import moment from 'moment';

@Injectable({
  providedIn: 'root'
})
export class SchedulerService extends BaseCrudService {
  getBaseAPIPath() {
    return `${API_URL.services}/${API_URL.app}/${API_URL.scheduleLessonDetails.root}`;
  }

  getClassTypeBorder(classType: number | undefined): string {
    switch (classType) {
      case ClassTypes.INTRODUCTORY:
        return Constants.staticImages.icons.introductoryLessonBorder;
      case ClassTypes.GROUP_CLASS:
        return Constants.staticImages.icons.groupClassLessonBorder;
      case ClassTypes.SUMMER_CAMP:
        return Constants.staticImages.icons.summerCampBorder;
      case ClassTypes.MAKE_UP:
        return Constants.staticImages.icons.makeUpLessonBorder;
        case ClassTypes.ENSEMBLE_CLASS:
        return Constants.staticImages.icons.ensembleCalssBorder;
      case ClassTypes.RECURRING:
        return '';
      default:
        return '';
    }
  }

  getClassType(classType: number | undefined): string {
    switch (classType) {
      case ClassTypes.INTRODUCTORY:
        return 'Introductory';
      case ClassTypes.RECURRING:
        return 'Recurring';
      case ClassTypes.GROUP_CLASS:
        return 'Group';
      case ClassTypes.SUMMER_CAMP:
        return 'Summer Camp';
      case ClassTypes.MAKE_UP:
        return 'Make-Up';
      case ClassTypes.ENSEMBLE_CLASS:
        return 'Ensemble';  
      default:
        return '';
    }
  }

  getLessonType(lessonType: number | undefined): string {
    return lessonType === LessonTypes.IN_PERSON ? 'In-Person' : 'Virtual';
  }

  getAgeLabelFromValue(value: number | undefined): string {
    return Constants.ageOptions.find((age) => age.value === value)?.label || '';
  }

  getNumberOfWeeks(startDate: string, endDate: string): number {
    const totalDays = moment(new Date(endDate)).diff(new Date(startDate), 'days');
    const weeks = Math.ceil(totalDays / 7);
    return weeks;
  }

  getDayOfWeek(value: string | Array<number>): string {
    return Constants.daysOfTheWeek.find((day) => day.value === Number(value))?.key || '';
  }

  getDaysOfWeek(value: Array<number>): string {
    return value.map((day) => Constants.daysOfTheWeek.find((d) => d.value === day)?.label).join(', ');
  }

  getNumberOfDays(startDate: string, endDate: string): number {
    return moment(new Date(endDate)).diff(new Date(startDate), 'days');
  }

  getScheduleBackgroundColor(startDate: string, isDraftSchedule: boolean, instrumentColor: string,
    classType: number | null): string {
    if (this.isPastDate(startDate)) {
      return Constants.disabledEventColors.backgroundColor;
    }
    if(isDraftSchedule){
      return Constants.colors.blackShadeColor;
    }else {
      switch (classType) {
       
        case ClassTypes.SUMMER_CAMP:
          return Constants.colors.lightBlueColor;
          case ClassTypes.ENSEMBLE_CLASS:
          return Constants.colors.ensembleclasscolor;
        default:
          return instrumentColor;
      }

    }
  }

  getScheduleBorderImage(startDate: string, isDraftSchedule: boolean, classType: number): string {
    if (this.isPastDate(startDate)) {
      return Constants.disabledEventColors.backgroundColor;
    }
    return isDraftSchedule
      ? 'url(' + Constants.staticImages.icons.draftScheduleBorder
      : 'url(' + this.getClassTypeBorder(classType) + ')';
  }

  getScheduleColor(startDate: string, isDraftSchedule: boolean, instrumentFontColor: string,classType:number ): string {
    if (this.isPastDate(startDate)) {
      return Constants.disabledEventColors.color;
    }
    if(isDraftSchedule){
      return Constants.colors.blackColor;
    }else {
      switch (classType) { 
        case ClassTypes.SUMMER_CAMP:
          return Constants.colors.blueColor;
          case ClassTypes.ENSEMBLE_CLASS:
          return Constants.colors.whiteColor;
        default:
          return instrumentFontColor;
      }
    }
  }

  isPastDate(startDate: string): boolean {
    return moment(startDate).isBefore(moment());
  }

  getGroupClassName(lessonType: number, instrumentName: string): string {
    return `${lessonType === LessonTypes.IN_PERSON ? 'In-Person' : 'Duet™  Virtual'} ${instrumentName} Group Class`;
  }

  getEnsembleClassName(lessonType: number, instrumentName: string): string {
    return `${lessonType === LessonTypes.IN_PERSON ? 'In-Person' : 'Duet™  Virtual'} ${instrumentName} Ensemble Class`;
  }
}
