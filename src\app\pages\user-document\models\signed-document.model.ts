import { PlanDetailWrapper } from "../../settings/pages/plan/models";

export interface SignedDocuments {
  signedDocuments: SignedDocumentsInfo;
}

export interface SignedDocumentsInfo {
  id: number | undefined;
  studentId: number | undefined;
  documentId: number | undefined;
  documentName: string;
  documentType: number | undefined;
  filePath: string;
  uploadDate: string;
  isAgreementDone: boolean;
  agreementDate: string;
  documentSendBy: string;
  fullFilePath: string;
  uploadLocalDate: string;
  recurringScheduleId: number;
  amount: number;
  isPaid: boolean;
  serviceFees: number;
  registrationFees: number;
  discountedAmount: number;
  planAmount: number;
  planId: number;
  daysOfSchedule: Array<number>;
  planDetails: PlanDetailWrapper[];
  planInstrument: string;
  planStartDate: string;
  planType: number;
  isRecurringDiscount: boolean;
  totalAmount: number;
}

// Interface for plan billing details
export interface PlanBillingDetails {
  planCost: number;
  serviceCharge: number;
  registrationFees: number;
  discountAmount: number;
  totalAmount: number;
}

// Interface for current month payment
export interface CurrentMonthPayment {
  amount: number;
  dueDate: string;
  description: string;
}

// Interface for recurring payment schedule
export interface RecurringPaymentSchedule {
  monthlyAmount: number;
  nextBillingDate: string;
  billingCycle: string;
  futurePayments: FuturePayment[];
}

export interface FuturePayment {
  date: string;
  amount: number;
  description: string;
}
