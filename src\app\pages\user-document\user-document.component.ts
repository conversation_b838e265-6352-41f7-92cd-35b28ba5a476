import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule } from '@angular/common';
import { DirectivesModule } from 'src/app/shared/directives/directives.module';
import { DocumentTypes } from '../settings/pages/document/models';
import { CBResponse } from 'src/app/shared/models';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { SignedDocumentService } from './services';
import { SignedDocuments, SignedDocumentsInfo } from './models';
import { SharedModule } from '../../shared/shared.module';
import { MatSidenavModule } from '@angular/material/sidenav';
import { ActivatedRoute, Router } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Debounce } from 'src/app/shared/decorators';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { PdfViewerComponent } from './pages/pdf-viewer/pdf-viewer.component';
import { DateUtils } from 'src/app/shared/utils/date.utils';

const DEPENDENCIES = {
  MODULES: [
    CommonModule,
    DirectivesModule,
    MatFormFieldModule,
    SharedModule,
    MatSidenavModule,
    MatInputModule,
    ReactiveFormsModule,
    FormsModule,
    MatIconModule,
    MatButtonModule
  ],
  COMPONENTS: [PdfViewerComponent]
};

@Component({
  selector: 'app-user-document',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './user-document.component.html',
  styleUrl: './user-document.component.scss'
})
export class UserDocumentComponent extends BaseComponent implements OnInit {
  pageTabOptions = { MAIN: 'Main Document', RENTAL: 'Rental Instrument Document' };
  selectedTabOption = this.pageTabOptions.MAIN;

  documentDetails!: SignedDocuments[];
  filteredDocumentDetails: SignedDocuments[] = [];
  documentTypes = DocumentTypes;
  searchTerm!: string;
  documentInfo!: SignedDocumentsInfo;
  isDocumentSideNavOpen = false;
  shrinkTrue = false;

  constructor(
    private readonly signedDocumentService: SignedDocumentService,
    private readonly cdr: ChangeDetectorRef,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router
  ) {
    super();
  }

  ngOnInit(): void {
    this.setActiveTabFromQueryParams();
  }

  setActiveTabFromQueryParams(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (Object.keys(params).length) {
        this.selectedTabOption = params.activeTab;
        this.getDocumentDetails();
        return;
      }
      this.setActiveTabOption(this.pageTabOptions.MAIN);
    });
  }

  getDocumentDetails(): void {
    this.showPageLoader = true;
    this.cdr.detectChanges();
    this.signedDocumentService
      .getList<CBResponse<SignedDocuments>>(API_URL.crud.getAll)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SignedDocuments>) => {
          this.setUploadedLocalDate(res);
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  setUploadedLocalDate(res: CBResponse<SignedDocuments>) {
    this.documentDetails = res.result.items.map((document: SignedDocuments) => {
      return {
        ...document,
        signedDocuments: {
          ...document.signedDocuments,
          uploadLocalDate: DateUtils.toLocal(
            document.signedDocuments.uploadDate,
            'yyyy-MM-DDTHH:mm:ss.SSS+0000',
            'yyyy-MM-DDTHH:mm:ss'
          )
        }
      };
    });

    this.filteredDocumentDetails = res.result.items.map((document: SignedDocuments) => {
      return {
        ...document,
        signedDocuments: {
          ...document.signedDocuments,
          uploadLocalDate: DateUtils.toLocal(
            document.signedDocuments.uploadDate,
            'yyyy-MM-DDTHH:mm:ss.SSS+0000',
            'yyyy-MM-DDTHH:mm:ss'
          )
        }
      };
    });
  }

  openPDFViewer(documentInfo: SignedDocumentsInfo): void {
    this.isDocumentSideNavOpen = true;
    this.documentInfo = documentInfo;
  }

  setActiveTabOption(tabName: string): void {
    this.selectedTabOption = tabName;
    this.router.navigate([this.path.document.root], {
      queryParams: {
        activeTab: tabName
      }
    });
  }

  @Debounce(100)
  onSearchTermChanged(): void {
    if (this.searchTerm) {
      this.filteredDocumentDetails = this.documentDetails.filter((doc: SignedDocuments) =>
        doc.signedDocuments.documentName.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    } else {
      this.getDocumentDetails();
    }
    this.cdr.detectChanges();
  }
}
