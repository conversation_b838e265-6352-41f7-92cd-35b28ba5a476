<ng-container>
  <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : !cardDetailsFlag ? addCard : cardDetailsData"></ng-container>
</ng-container>
<ng-template #addCard>
  <app-ach-method
    [screen]="screen"
    (closeSideNav)="closeSideNavClick()"
    [savedAddressDetails]="savedAddressDetails"
    [cardDetails]="cardDetails"
    [selectedCardDetail]="selectedCardDetail"
    [isAddCard]="isAddCard"
    (cardDetailsFlag)="cardDetailsFlagChange($event)"
  ></app-ach-method>
</ng-template>
<ng-template #cardDetailsData>
  <div class="auth-page-wrapper auth-page-with-header">
    <div class="card-container">
      @for (card of cardDetails; track $index) {
      <div class="card-details">
        <div class="o-card mb-2">
          <div class="o-card-body pointer">
            <div class="account-content">
              <div class="account-section">
                <div class="section-header">
                  <mat-icon class="section-icon">account_balance</mat-icon>
                  <h3 class="section-title">Account Information</h3>
                </div>
                <div class="section-body">
                  <div class="detail-row">
                    <span class="detail-label">Account Name:</span>
                    <span class="detail-value">{{ card.accountName }}</span>
                  </div>
                  <div class="detail-row">
                    <span class="detail-label">Account Number:</span>
                    <span class="detail-value"> {{ card.accountNumber }}</span>
                  </div>
                  <div class="detail-row">
                    <span class="detail-label">Routing Number:</span>
                    <span class="detail-value"> {{ card.routingNumber }}</span>
                  </div>
                </div>
              </div>

              <div class="account-section">
                <div class="section-header">
                  <mat-icon class="section-icon">location_on</mat-icon>
                  <h3 class="section-title">Billing Address</h3>
                </div>
                <div class="section-body">
                  <div class="detail-row">
                    <span class="detail-value name-value">{{ card.firstName }} {{ card.lastName }}</span>
                  </div>
                  <div class="detail-row">
                    <span class="detail-value">{{ card.address }}</span>
                  </div>
                  <div class="detail-row">
                    <span class="detail-value">{{ card.city }}, {{ card.state }} {{ card.zip }}</span>
                  </div>
                </div>
              </div>
            </div>
            @if (screen == 'billing-screen') {
            <div class="card-info">
              <div class="card-buttons">
                <img
                  class="pointer"
                  alt="delete"
                  [src]="constants.staticImages.icons.redTrash"
                  alt="pen"
                  class="editImg"
                  (click)="deleteAchMethod(card)"
                />
              </div>
            </div>
            }
          </div>
        </div>
      </div>
      }
      <div class="action-btn-wrapper">
        <div class="tab-btn-content">
          @if (screen !== 'billing-screen') {
          <button
            mat-raised-button
            id="payButton"
            color="primary"
            class="mat-primary-btn me-2"
            [appLoader]="showBtnLoader"
            (click)="savedCardPayment()"
            type="button"
          >
            Pay
          </button>
          }
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
