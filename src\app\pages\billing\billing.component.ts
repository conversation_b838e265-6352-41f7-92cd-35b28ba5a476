import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule } from '@angular/common';
import { DirectivesModule } from 'src/app/shared/directives/directives.module';
import { DocumentTypes } from '../settings/pages/document/models';
import { CBResponse } from 'src/app/shared/models';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { SharedModule } from '../../shared/shared.module';
import { MatSidenavModule } from '@angular/material/sidenav';
import { ActivatedRoute, Router } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { DateUtils } from 'src/app/shared/utils/date.utils';
import { SignedDocuments, SignedDocumentsInfo } from '../user-document/models';
import { SignedDocumentService } from '../user-document/services';
import { PaymentMethodsComponent } from '../../shared/components/payment-methods/payment-methods.component';
import { PaymentService } from '../schedule-classes/services';

const DEPENDENCIES = {
  MODULES: [
    CommonModule,
    DirectivesModule,
    MatFormFieldModule,
    SharedModule,
    MatSidenavModule,
    MatInputModule,
    ReactiveFormsModule,
    FormsModule,
    MatIconModule,
    MatButtonModule
  ],
  components: [PaymentMethodsComponent]
};

@Component({
  selector: 'app-billing',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.components],
  templateUrl: './billing.component.html',
  styleUrl: './billing.component.scss'
})
export class BillingComponent extends BaseComponent implements OnInit {
  @Input() selectedTabOption!: string;
  
  pageTabOptions = { MAIN: 'Open Bill', RENTAL: 'Bill History' };
  // selectedTabOption = this.pageTabOptions.MAIN;
  documentDetails!: SignedDocuments[];
  filteredDocumentDetails: SignedDocuments[] = [];
  documentInfo!: SignedDocumentsInfo;

  documentTypes = DocumentTypes;
  searchTerm!: string;
  isDocumentSideNavOpen = false;

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly signedDocumentService: SignedDocumentService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly paymentService: PaymentService
  ) {
    super();
  }

  ngOnInit(): void {
    this.setActiveTabFromQueryParams();
  }

  setActiveTabFromQueryParams(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (Object.keys(params).length) {
        this.selectedTabOption = params.activeTab;
        this.getDocumentDetails();
        return;
      }
      this.setActiveTabOption(this.pageTabOptions.MAIN);
    });
  }

  getDocumentDetails(): void {
    this.showPageLoader = true;
    this.cdr.detectChanges();
    this.signedDocumentService
      .getList<CBResponse<SignedDocuments>>(API_URL.crud.getAll)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SignedDocuments>) => {
          this.setUploadedLocalDate(res);
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  setUploadedLocalDate(res: CBResponse<SignedDocuments>) {
    this.documentDetails = res.result.items.map((document: SignedDocuments) => {
      return {
        ...document,
        signedDocuments: {
          ...document.signedDocuments,
          uploadLocalDate: DateUtils.toLocal(document.signedDocuments.uploadDate, 'yyyy-MM-DDTHH:mm:ss.SSS+0000', 'yyyy-MM-DDTHH:mm:ss')
        }
      };
    });

    this.filteredDocumentDetails = res.result.items.map((document: SignedDocuments) => {
      return {
        ...document,
        signedDocuments: {
          ...document.signedDocuments,
          uploadLocalDate: DateUtils.toLocal(document.signedDocuments.uploadDate, 'yyyy-MM-DDTHH:mm:ss.SSS+0000', 'yyyy-MM-DDTHH:mm:ss')
        }
      };
    });
  }

  openPDFViewer(documentInfo: SignedDocumentsInfo): void {
    this.isDocumentSideNavOpen = true;
    this.documentInfo = documentInfo;
  }

  openDialog(): void {
    this.isDocumentSideNavOpen = true;
  }

  setActiveTabOption(tabName: string): void {
    this.selectedTabOption = tabName;
    this.router.navigate([this.path.billing.root], {
      queryParams: {
        activeTab: tabName
      }
    });
  }
}
