<div class="plan-recurring-payment-container">
    <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : paymentContent"></ng-container>
</div>

<ng-template #paymentContent>
    <!-- Plan Details Section -->
    <div class="o-card mb-4">
        <div class="o-card-body">
            <div class="card-header">
                <div class="card-title">
                    <mat-icon>description</mat-icon>
                    Plan Details
                </div>
            </div>

            <div class="plan-details-grid">
                <div class="plan-detail-item">
                    <div class="detail-label">Plan Name</div>
                    <div class="detail-value">{{ planDetails.planName }}</div>
                </div>

                <div class="plan-detail-item">
                    <div class="detail-label">Class Type</div>
                    <div class="detail-value">{{ planDetails.classType }}</div>
                </div>

                <div class="plan-detail-item">
                    <div class="detail-label">Visit Per Week</div>
                    <div class="detail-value">{{ planDetails.visitsPerWeek }} visit{{ planDetails.visitsPerWeek > 1 ? 's' : '' }}</div>
                </div>

                <div class="plan-detail-item">
                    <div class="detail-label">Instrument</div>
                    <div class="detail-value">{{ planDetails.instrument }}</div>
                </div>

                <div class="plan-detail-item">
                    <div class="detail-label">Start From</div>
                    <div class="detail-value">{{ formatDate(planDetails.startDate) }} | {{ planDetails.startTime }}</div>
                </div>

                <div class="plan-detail-item">
                    <div class="detail-label">Plan Amount</div>
                    <div class="detail-value plan-amount">{{ formatCurrency(planDetails.planAmount) }}/Month</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Default Payment Method Section -->
    <div class="o-card mb-4">
        <div class="o-card-body">
            <div class="card-header">
                <div class="card-title">
                    <mat-icon>{{ getPaymentMethodIcon() }}</mat-icon>
                    Default Payment Method
                </div>
            </div>

            <div class="payment-method-wrapper">
                @if (defaultPaymentMethod) {
                    <div class="payment-method-display">
                        @if (defaultPaymentMethod === 'card' && defaultCard) {
                        <div class="card-number">{{ defaultCard.ccType | uppercase}} **** {{ defaultCard.ccNum }}</div>
                        <div class="card-exp text-truncate">Expires {{ defaultCard.ccExpiry.slice(0, 2) }}/{{
                            defaultCard.ccExpiry.slice(2) }}</div>
                        }
                        @else if (defaultPaymentMethod === 'ach' && defaultAch) {
                        <div class="account-content">
                            <div class="account-section">
                                <div class="section-header">
                                    <mat-icon class="section-icon">account_balance</mat-icon>
                                    <div class="section-title">Account Information</div>
                                </div>
                                <div class="section-body">
                                    <div class="detail-row">
                                        <span class="detail-label">Account Name:</span>
                                        <span class="detail-value">{{ defaultAch.accountName }}</span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">Account Number:</span>
                                        <span class="detail-value"> {{ defaultAch.accountNumber }}</span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">Routing Number:</span>
                                        <span class="detail-value"> {{ defaultAch.routingNumber }}</span>
                                    </div>
                                </div>
                            </div>
        
                            <div class="account-section">
                                <div class="section-header">
                                    <mat-icon class="section-icon">location_on</mat-icon>
                                    <div class="section-title">Billing Address</div>
                                </div>
                                <div class="section-body">
                                    <div class="detail-row">
                                        <span class="detail-value name-value">{{ defaultAch.firstName }} {{
                                            defaultAch.lastName }}</span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-value">{{ defaultAch.address }}</span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-value">{{ defaultAch.city }}, {{ defaultAch.state }} {{
                                            defaultAch.zip }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        }
                    </div>
                    <div class="payment-note mt-3">
                        <mat-icon>info</mat-icon>
                        <span>This is your default payment method for all future payments. You can change it from <span class="clickable" (click)="navigateToBillingSection()"> billing section</span>.</span>
                    </div>
                }
                @else {
                    <div class="no-payment-method">
                        <mat-icon>warning</mat-icon>
                        <span>No default payment method set. Please add a payment method from <span class="clickable" (click)="navigateToBillingSection()"> billing section</span> to continue.</span>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Billing Details Section -->
    <div class="o-card mb-4">
        <div class="o-card-body">
            <div class="card-header">
                <div class="card-title">
                    <mat-icon>receipt</mat-icon>
                    First Payment
                </div>
            </div>

            <div class="billing-breakdown">
                <div class="billing-item">
                    <div class="billing-label">Current Month Plan Payment</div>
                    <div class="billing-value">${{ currentMonthPayment }}</div>
                </div>

                <div class="billing-item">
                    <div class="billing-label">Service Charge</div>
                    <div class="billing-value">+${{ selectedPlanDetail?.serviceFees }}</div>
                </div>

                <div class="billing-item">
                    <div class="billing-label">Registration Fees</div>
                    <div class="billing-value">+${{ selectedPlanDetail?.registrationFees }}</div>
                </div>

                <div class="billing-item discount pb-0">
                    <div class="billing-label">Discount Applied</div>
                    <div class="billing-value">-${{ selectedPlanDetail?.discountAmount }}</div>
                </div>

                <div class="dotted-divider"></div>

                <div class="billing-item total p-0">
                    <div class="billing-label">Total Amount</div>
                    <div class="billing-value">${{ calculateTotalAmount }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Month Payment Section -->
    <!-- <div class="o-card mb-4">
        <div class="o-card-body">
            <div class="card-header">
                <div class="card-title">
                    <mat-icon>today</mat-icon>
                    First Payment
                </div>
            </div>

            <div class="current-payment-info">
                <div class="payment-amount-section">
                    <div class="payment-amount">{{ formatCurrency(currentMonthPayment.amount) }}</div>
                    <div class="payment-description">{{ currentMonthPayment.description }}</div>
                </div>

                <div class="payment-due-section">
                    <div class="due-label">Due Date</div>
                    <div class="due-date">{{ formatDate(currentMonthPayment.dueDate) }}</div>
                </div>
            </div>

            <div class="payment-note">
                <mat-icon>info</mat-icon>
                <span>This is a one-time payment for the current month including registration fees and setup
                    charges.</span>
            </div>
        </div>
    </div> -->

    <!-- Recurring Payment Schedule Section -->
    <!-- <div class="o-card mb-4">
        <div class="o-card-body">
            <div class="card-header">
                <div class="card-title">
                    <mat-icon>schedule</mat-icon>
                    Recurring Payment Schedule
                </div>
            </div>

            <div class="recurring-info">
                <div class="recurring-summary">
                    <div class="summary-item">
                        <div class="summary-label">Monthly Amount</div>
                        <div class="summary-value">{{ formatCurrency(recurringPaymentSchedule.monthlyAmount) }}</div>
                    </div>

                    <div class="summary-item">
                        <div class="summary-label">Billing Cycle</div>
                        <div class="summary-value">{{ recurringPaymentSchedule.billingCycle }}</div>
                    </div>

                    <div class="summary-item">
                        <div class="summary-label">Next Billing Date</div>
                        <div class="summary-value">{{ formatDate(recurringPaymentSchedule.nextBillingDate) }}</div>
                    </div>
                </div>

                <div class="future-payments">
                    <div class="future-payments-header">
                        <h4>Upcoming Payments</h4>
                    </div>

                    <div class="payments-list">
                        <div class="payment-item"
                            *ngFor="let payment of recurringPaymentSchedule.futurePayments; let i = index">
                            <div class="payment-date">
                                <mat-icon>event</mat-icon>
                                {{ formatDate(payment.date) }}
                            </div>
                            <div class="payment-details">
                                <div class="payment-amount">{{ formatCurrency(payment.amount) }}</div>
                                <div class="payment-desc">{{ payment.description }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="recurring-note">
                <mat-icon>autorenew</mat-icon>
                <span>Payments will be automatically charged to your default payment method on the scheduled
                    dates.</span>
            </div>
        </div>
    </div> -->
</ng-template>

<ng-template #showLoader>
    <div class="page-loader-wrapper">
        <app-content-loader></app-content-loader>
    </div>
</ng-template>