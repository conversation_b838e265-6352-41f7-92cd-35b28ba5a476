import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../../shared.module';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { AllCustomerCards, CardDetailsResponse, PaymentParams } from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FormsModule } from '@angular/forms';
import { AuthService } from 'src/app/auth/services';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from '../confirmation-dialog/confirmation-dialog.component';
import { API_URL } from '../../constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';
import { Account, Address } from 'src/app/auth/models/user.model';
import { AppToasterService } from '../../services';
import { PaymentFormComponent } from '../payment-form/payment-form.component';
import { MatRadioModule } from '@angular/material/radio';
import { BaseComponent } from '../base-component/base.component';
import { CBGetResponse } from '../../models';

const DEPENDENCIES = {
  MODULES: [MatButtonModule, SharedModule, FormsModule, CommonModule, MatCheckboxModule, MatIconModule, MatRadioModule],
  COMPONENTS: [PaymentFormComponent]
};

@Component({
  selector: 'app-card-method',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './card-method.component.html',
  styleUrl: './card-method.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CardMethodComponent extends BaseComponent implements OnInit {
  @Input() screen!: string;
  @Input() accManagerDetails!: Account | undefined;

  cardDetailsFlag = false;
  cardDetails: PaymentParams[] = [];
  selectedCardDetail!: PaymentParams;
  savedAddressDetails!: Address;
  isAddCard = true;

  @Output() sendSelectedCardDetail = new EventEmitter<PaymentParams>();
  @Output() allCustomerCards = new EventEmitter<AllCustomerCards>();
  @Output() closeSideNav = new EventEmitter<void>();

  constructor(
    private readonly paymentService: PaymentService,
    private readonly authService: AuthService,
    private readonly cdr: ChangeDetectorRef,
    private readonly dialog: MatDialog,
    private readonly toasterService: AppToasterService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCurrentUser();
    this.cdr.detectChanges();
  }

  getAllCustomerCards(): void {
    this.paymentService
      .getList<CBGetResponse<AllCustomerCards>>(`${API_URL.payment.getAllCustomerCards}?userId=${this.currentUser?.userId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<AllCustomerCards>) => {
          this.cardDetails = res?.result?.getAllCardsOfUser || [];
          this.selectedCardDetail = res?.result?.getAllCardsOfUser.find((card: PaymentParams) => card.isDefault) || ({} as PaymentParams);
          this.cardDetailsFlag = this.cardDetails.length > 0;
          this.allCustomerCards.emit(res?.result);
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getCurrentUser(): void {
    this.showPageLoader = true;
    this.authService
      .getCurrentUser(true)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res;
          if (this.constants.roles.ADMIN === this.currentUser?.userRole) {
            this.savedAddressDetails = this.getAddress(this.accManagerDetails!);
          } else {
            this.savedAddressDetails = this.currentUser ? this.getAddress(this.currentUser) : ({} as Address);
          }
          this.showPageLoader = false;
          this.getAllCustomerCards();
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getAddress(user: Account): Address {
    return {
      firstName: user?.firstName,
      lastName: user?.lastName,
      address: user?.address,
      city: user?.city || '',
      state: user?.state || '',
      zipCode: user?.zipCode || ''
    };
  }

  changeEvent(card: PaymentParams): void {
    this.selectedCardDetail = card;
    this.sendSelectedCardDetail.emit(this.selectedCardDetail);
  }

  savedCardPayment(): void {
    this.showBtnLoader = true;
    this.paymentService.setUserPayment(this.getCardDetailsUsingSavedCard(this.selectedCardDetail));
  }

  getCardDetailsUsingSavedCard(card: PaymentParams): CardDetailsResponse {
    return {
      isUsingSavedCard: true,
      customerVaultId: card.customerVaultId
    };
  }

  cardDetailsFlagChange(flag: boolean): void {
    this.cardDetailsFlag = flag;
    if (this.cardDetailsFlag) {
      this.getAllCustomerCards();
    }
  }

  showAddCard(): void {
    this.isAddCard = true;
    this.cardDetailsFlag = false;
    this.cdr.detectChanges();
  }

  editCard(card: PaymentParams): void {
    this.isAddCard = false;
    this.selectedCardDetail = card;
    this.cardDetailsFlag = false;
    this.cdr.detectChanges();
  }

  deleteCard(card: PaymentParams): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Card Information`,
        message: `Are you sure you want to delete this Card information?`
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.deleteCardDetails(card);
        this.cdr.detectChanges();
      }
    });
  }

  deleteCardDetails(card: PaymentParams): void {
    this.showPageLoader = true;
    this.paymentService
      .deleteCard(card.customerVaultId as string)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.constants.successMessages.deletedSuccessfully.replace('{item}', 'Card'));
          this.getAllCustomerCards();
          this.cdr.detectChanges();
        }
      });
  }

  closeSideNavClick(): void {
    this.closeSideNav.emit();
  }

  savedCardAsDefault(): void {
    this.showBtnLoader = true;
    this.paymentService.add(this.getSaveAsDefaultCard(), API_URL.payment.setDefaultPaymentMethod).subscribe({
      next: () => {
        this.showBtnLoader = false;
        this.toasterService.success(this.constants.successMessages.addCustomerCardAsDefault);
        this.cdr.detectChanges();
      },
      error: () => {
        this.showBtnLoader = false;
        this.cdr.detectChanges();
      }
    });
  }

  getSaveAsDefaultCard(): PaymentParams {
    return {
      userId: this.currentUser?.userId,
      customerVaultId: this.selectedCardDetail.customerVaultId
    };
  }
}
