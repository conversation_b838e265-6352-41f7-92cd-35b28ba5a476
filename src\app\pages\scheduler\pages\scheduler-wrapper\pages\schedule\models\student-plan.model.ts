import { PlanDetailWrapper } from 'src/app/pages/settings/pages/plan/models';

export interface StudentPlans {
  id: number;
  assignedPlanStatus: number;
  studentplan: StudentPlanInfo;
  profilePhoto: string;
  plan: number;
  planType: number;
  dependentInformationDisplayProperty: string;
  dependentName: string;
  instrumentName: string;
  planPrice: number;
  isDeleted: boolean;
  instrumentId: number | null;
  documentName: string;
  documentId: number;
  planDetails: PlanDetailWrapper[];
  studentDiscontinuedPlanDetails: StudentDiscontinuedPlanDetails | null;
  isEnsembleAvailable: boolean;
  nextRenewalDate: string;
  registrationFees: number;
  serviceFees: number;
  discountedAmount: number;
  isRecurringDiscount: boolean;
}

export interface StudentPlanInfo {
  id: number | null;
  planSummaryId: number;
  dependentInformationId: number;
  instrumentDetailId: number;
}

export interface StudentDiscontinuedPlanDetails {
  approvedDate: string;
  requestDate: string;
  reason: string;
  approveRemark: string;
  planCancelRequestId: number;
  planCancelRequestStatus: number;
  localApprovedDate: string | undefined;
  localRequestDate: string | undefined;
  localCanceledDate: string | undefined;
  requestedBy: string;
  approvedBy: string;
  canceledBy: string;
  canceledDate: string;
  assignedPlanStatus: number;
  isEnsembleAvailable: boolean;
}

export enum AssignedPlanStatus {
  ASSIGNED_PLAN = 1,
  SCHEDULE_CREATED = 2,
  PAYMENT_DONE = 3
}
