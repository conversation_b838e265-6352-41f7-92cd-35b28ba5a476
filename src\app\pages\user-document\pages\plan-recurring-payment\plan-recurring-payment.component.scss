@import 'src/assets/scss/theme/_mixins.scss';
@import 'src/assets/scss/variables';

.plan-recurring-payment-container {
  .o-card {
    background-color: $gray-bg-light;

    .card-header {
      @include flex-content-space-between;
      margin-bottom: 20px;

      .card-title {
        font-size: 18px;
        font-weight: 700;
        @include flex-content-align-center;

        mat-icon {
          margin-right: 10px;
          color: $primary-color;
          font-size: 22px;
        }
      }
    }

    // Default Payment Method Styles
    .payment-method-display {
      padding: 20px;
      background-color: $white-color;
      border-radius: 8px;
      border: 2px solid $primary-color;

      .card-number {
        font-size: 17px;
        font-weight: 700;
        width: fit-content;
      }

      .card-exp {
        color: $gray-text;
        font-size: 14px;
      }

      .account-content {
        @include flex-content-space-between;

        .account-section {
          .section-header {
            @include flex-content-align-center;
            font-weight: 600;
            margin-bottom: 12px;

            .section-title {
              font-size: 17px;
            }
          }

          .section-body {
            .detail-row {
              margin-bottom: 6px;

              .detail-label,
              .detail-value {
                font-size: 15px;
              }
            }
          }
        }
      }
    }

    .no-payment-method {
      @include flex-content-align-center;
      padding: 20px;
      background-color: $light-yellow-color;
      border: 1px solid $light-yellow-color;
      border-radius: 8px;
      color: $yellow-color;

      mat-icon {
        margin-right: 10px;
        filter: $yellow-filter;
      }
    }

    .clickable {
      color: $primary-color !important;
      text-decoration: underline;
      cursor: pointer;
    }

    // Billing Details Styles
    .billing-breakdown {
      .billing-item {
        @include flex-content-space-between;
        padding-bottom: 15px;
        border-bottom: 1px solid $gray-bg-light;

        &:last-child {
          border-bottom: none;
          padding-bottom: 0;
        }

        &.discount {
          .billing-value {
            color: $primary-color;
            font-weight: 600;
          }
        }

        &.total {
          padding-top: 15px;
          font-weight: 700;
          font-size: 16px;

          .billing-value {
            color: $primary-color;
          }
        }

        .billing-label {
          font-size: 16px;
          color: $gray-text;
        }

        .billing-value {
          font-size: 16px;
          font-weight: 600;
          color: $gray-text;
        }
      }
    }

    // Current Month Payment Styles
    .current-payment-info {
      @include flex-content-space-between;
      align-items: center;
      margin-bottom: 20px;

      .payment-amount-section {
        .payment-amount {
          font-size: 24px;
          font-weight: 700;
          color: $primary-color;
          margin-bottom: 5px;
        }

        .payment-description {
          font-size: 14px;
          color: $gray-text;
        }
      }

      .payment-due-section {
        text-align: right;

        .due-label {
          font-size: 12px;
          color: $gray-text;
          text-transform: uppercase;
          margin-bottom: 5px;
        }

        .due-date {
          font-size: 14px;
          font-weight: 600;
          color: $gray-text;
        }
      }
    }

    .payment-note {
      @include flex-content-align-center;
      padding: 10px;
      background-color: $secondary-color;
      border-radius: 8px;

      mat-icon {
        margin-right: 10px;
        font-size: 18px;
      }

      span {
        font-size: 13px;
        color: $gray-text;
      }
    }

    // Recurring Payment Schedule Styles
    // .recurring-info {
    //   .recurring-summary {
    //     display: grid;
    //     grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    //     gap: 20px;
    //     margin-bottom: 30px;
    //     padding: 20px;
    //     background-color: $gray-bg-light;
    //     border-radius: 8px;

    //     .summary-item {
    //       text-align: center;

    //       .summary-label {
    //         font-size: 12px;
    //         color: $gray-text;
    //         text-transform: uppercase;
    //         margin-bottom: 8px;
    //       }

    //       .summary-value {
    //         font-size: 16px;
    //         font-weight: 600;
    //         color: $gray-text;
    //       }
    //     }
    //   }

    //   .future-payments {
    //     .future-payments-header {
    //       margin-bottom: 20px;

    //       h4 {
    //         font-size: 16px;
    //         font-weight: 600;
    //         color: $gray-text;
    //         margin: 0;
    //       }
    //     }

    //     .payments-list {
    //       .payment-item {
    //         @include flex-content-space-between;
    //         align-items: center;
    //         padding: 15px 0;
    //         border-bottom: 1px solid $gray-bg-light;

    //         &:last-child {
    //           border-bottom: none;
    //         }

    //         .payment-date {
    //           @include flex-content-align-center;
    //           font-size: 14px;
    //           color: $gray-text;

    //           mat-icon {
    //             margin-right: 8px;
    //             font-size: 18px;
    //             color: $primary-color;
    //           }
    //         }

    //         .payment-details {
    //           text-align: right;

    //           .payment-amount {
    //             font-size: 16px;
    //             font-weight: 600;
    //             color: $gray-text;
    //             margin-bottom: 3px;
    //           }

    //           .payment-desc {
    //             font-size: 12px;
    //             color: $gray-text;
    //           }
    //         }
    //       }
    //     }
    //   }
    // }

    // .recurring-note {
    //   @include flex-content-align-center;
    //   padding: 15px;
    //   background-color: #f3e5f5;
    //   border-radius: 8px;
    //   border-left: 4px solid #9c27b0;
    //   margin-top: 20px;

    //   mat-icon {
    //     margin-right: 10px;
    //     color: #9c27b0;
    //     font-size: 18px;
    //   }

    //   span {
    //     font-size: 13px;
    //     color: $gray-text;
    //   }
    // }

    // Page loader styles
    .page-loader-wrapper {
      @include flex-content-center;
      padding: 60px 20px;
      min-height: 400px;
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 15px;

    .payment-method-display {
      flex-direction: column;
      align-items: flex-start;
      gap: 15px;
    }

    .current-payment-info {
      flex-direction: column;
      align-items: flex-start;
      gap: 15px;

      .payment-due-section {
        text-align: left;
      }
    }

    .recurring-info {
      .recurring-summary {
        grid-template-columns: 1fr;
        gap: 15px;
      }

      .future-payments {
        .payments-list {
          .payment-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;

            .payment-details {
              text-align: left;
            }
          }
        }
      }
    }
  }
}

::ng-deep {
  .mat-drawer-inner-container {
    overflow: hidden !important;
  }
}
