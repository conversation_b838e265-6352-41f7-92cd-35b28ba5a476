import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { BaseComponent } from '../base-component/base.component';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { CardDetailsResponse, PaymentParams } from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { AuthService } from 'src/app/auth/services';
import { Account, Address } from 'src/app/auth/models/user.model';
import { takeUntil } from 'rxjs';
import { AppToasterService } from '../../services';
import { SharedModule } from '../../shared.module';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { AddressFormComponent } from '../address-form/address-form.component';
import { API_URL } from '../../constants/api-url.constants';
import { TransactionTypes } from 'src/app/pages/shop/models';

declare const CollectJS: any;

const DEPENDENCIES = {
  MODULES: [SharedModule, CommonModule, FormsModule, MatCheckboxModule, MatButtonModule],
  COMPONENTS: [AddressFormComponent]
};

@Component({
  selector: 'app-ach-method',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './ach-method.component.html',
  styleUrls: ['./ach-method.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AchMethodComponent extends BaseComponent implements OnInit, AfterViewInit {
  @Input() screen!: string;
  @Input() accManagerDetails!: Account | undefined;
  @Output() closeSideNav = new EventEmitter<void>();
  @Input() savedAddressDetails!: Address;
  @Input() cardDetails: PaymentParams[] = [];
  @Input() selectedCardDetail: PaymentParams = {} as PaymentParams;
  @Output() cardDetailsFlag = new EventEmitter<boolean>();
  @Input() isAddCard: boolean = true;

  achDetails: CardDetailsResponse = {} as CardDetailsResponse;
  isSaveAch = false;
  isFormValid = false;

  constructor(
    private readonly paymentService: PaymentService,
    private readonly authService: AuthService,
    private readonly cdr: ChangeDetectorRef,
    private readonly toasterService: AppToasterService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCurrentUser();
  }

  ngAfterViewInit(): void {
    if (!(window as any)['CollectJS']) {
      return;
    }

    this.configureCollectJs();
  }

  getPaymentParams(response: any): PaymentParams {
    return {
      userId: this.currentUser?.userId,
      token: response.token,
      transactionType: TransactionTypes.ACH,
      isDefault: false,
      firstName: this.savedAddressDetails.firstName,
      lastName: this.savedAddressDetails.lastName,
      address: this.savedAddressDetails.address,
      city: this.savedAddressDetails.city,
      state: this.savedAddressDetails.state,
      zip: this.savedAddressDetails.zipCode,
      accountNumber: response.check.account,
      routingNumber: response.check.aba,
      accountName: response.check.name
    };
  }

  configureCollectJs(): void {
    CollectJS.configure({
      callback: this.handleTokenizationCallback.bind(this),
      customCss: this.getCssStyles(),
      invalidCss: this.getCssStyles(),
      validCss: this.getCssStyles(),
      paymentType: 'ck',
      fields: this.getFieldConfigurations()
    });
  }

  onAddAchMethod(): void {
    console.log(CollectJS, this, this.handleTokenizationCallback);
    if (!this.isFormValid) {
      this.toasterService.error(this.constants.errorMessages.invalidDetails.replace('{item}', 'ACH'));
      return;
    }
    this.showBtnLoader = true;
    this.handleTokenizationCallback.bind(this);
    this.cdr.detectChanges();
  }
  
  private handleTokenizationCallback(response: any): void {
    this.isFormValid = true;
    if (response.token) {
      this.showPageLoader = true;
      if (this.screen === 'billing-screen') {
        this.paymentService.add(this.getPaymentParams(response), API_URL.payment.addNMICustomer).subscribe({
          next: () => {
            this.showBtnLoader = false;
            this.toasterService.success(this.constants.successMessages.addedSuccessfully.replace('{item}', 'ACH details'));
            this.cancel();
            this.cdr.detectChanges();
          },
          error: () => {
            this.showBtnLoader = false;
            this.cdr.detectChanges();
          }
        });
      } else {
        this.paymentService.setUserPayment(this.getCardDetails(response));
        this.closeSideNav.emit();
      }
    } else {
      this.showBtnLoader = false;
    }
  }

  private getCssStyles(): { [key: string]: string } {
    return {
      color: '#7e8299',
      'background-color': '#F9F9F9',
      'border-color': '#F9F9F9',
      'border-width': '0px'
    };
  }

  private getFieldConfigurations(): { [key: string]: any } {
    return {
      checkaccount: {
        selector: '#checkaccount',
        title: 'Account Number',
        placeholder: 'Enter Account Number'
      },
      checkaba: {
        selector: '#checkaba',
        title: 'Routing Number',
        placeholder: 'Enter Routing Number'
      },
      checkname: {
        selector: '#checkname',
        title: 'Account Name',
        placeholder: 'Enter Account Name'
      }
    };
  }

  getCurrentUser(): void {
    this.showPageLoader = true;
    this.authService
      .getCurrentUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res;
          if (this.constants.roles.ADMIN === this.currentUser?.userRole) {
            this.savedAddressDetails = this.getAddress(this.accManagerDetails!);
          } else {
            this.savedAddressDetails = this.currentUser ? this.getAddress(this.currentUser) : ({} as Address);
          }
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getAddress(user: Account): Address {
    return {
      firstName: user?.firstName,
      lastName: user?.lastName,
      address: user?.address,
      city: user?.city || 'New York',
      state: user?.state || 'NY',
      zipCode: user?.zipCode || '10001'
    };
  }

  cancel(): void {
    this.cardDetailsFlag.emit(true);
  }

  onSaveAddress(addressData: Address): void {
    this.savedAddressDetails = addressData;
  }

  getCardDetails(response: any): CardDetailsResponse {
    return {
      token: response.token,
      number: response.card.number,
      expiry: response.card.exp,
      type: response.card.type,
      isSaveCard: true,
      isUsingSavedCard: false,
      customerVaultId: response.customerVaultId,
      address: this.savedAddressDetails.address,
      city: this.savedAddressDetails.city,
      state: this.savedAddressDetails.state,
      zip: this.savedAddressDetails.zipCode,
      firstName: this.savedAddressDetails.firstName,
      lastName: this.savedAddressDetails.lastName
    };
  }
}
