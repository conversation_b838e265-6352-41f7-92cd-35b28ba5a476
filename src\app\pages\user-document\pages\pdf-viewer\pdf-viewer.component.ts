import { CommonModule, DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { SignedDocumentsInfo } from '../../models';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FormsModule } from '@angular/forms';
import { SafePipe } from 'src/app/shared/pipe';
import { SignedDocumentService } from '../../services';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { Subscription, takeUntil } from 'rxjs';
import { AppToasterService } from 'src/app/shared/services';
import { CardDetailsResponse, PaymentParams } from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { ClassTypes } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { PlanRecurringPaymentComponent } from "../plan-recurring-payment/plan-recurring-payment.component";
const DEPENDENCIES = {
  MODULES: [MatButtonModule, SharedModule, CommonModule, MatCheckboxModule, FormsModule],
  PIPES: [SafePipe],
  COMPONENTS: [PlanRecurringPaymentComponent]
};

@Component({
  selector: 'app-pdf-viewer',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES, ...DEPENDENCIES.COMPONENTS, PlanRecurringPaymentComponent],
  templateUrl: './pdf-viewer.component.html',
  styleUrl: './pdf-viewer.component.scss'
})
export class PdfViewerComponent extends BaseComponent implements OnInit, OnChanges, OnDestroy {
  @Input() documentInfo!: SignedDocumentsInfo | null;
  @Input() selectedPlanDocument!: string | null;
  @Input() isFromAdmin!: boolean;

  cardDetails!: CardDetailsResponse;

  isAgreementDone = false;
  showPaymentTemplate = false;
  isRePayment = false;
  isAgreementSubmitted = false;
  private subscriptions = new Subscription();

  @Output() closeSideNav = new EventEmitter<void>();
  @Output() refereshDocuments = new EventEmitter<void>();
  @Output() shrinkMatSideNav = new EventEmitter<void>();

  constructor(
    private readonly signedDocumentService: SignedDocumentService,
    private readonly cdr: ChangeDetectorRef,
    private readonly toasterService: AppToasterService,
    private readonly datePipe: DatePipe,
    private readonly paymentService: PaymentService
  ) {
    super();
  }

  ngOnInit(): void {
    if (this.documentInfo?.isAgreementDone && !this.documentInfo?.isPaid && !this.isFromAdmin) {
      this.showPaymentTemplate = true;
      this.isAgreementSubmitted = true;
      this.showPageLoader = false;
    }
    if(this.showPaymentTemplate) {
      this.shrinkMatSideNav.emit();
    }
    this.getCardDetailsForPaymentInitiate();
  }

  getCardDetailsForPaymentInitiate(): void {
    this.subscriptions.add(
      this.paymentService.userCardDetails$.pipe(takeUntil(this.destroy$)).subscribe(card => {
        if (
          card &&
          this.showPaymentTemplate &&
          (!this.isAgreementSubmitted || this.documentInfo?.isAgreementDone) &&
          !this.documentInfo?.isPaid
        ) {
          this.cardDetails = card;
          this.initPaymentProcess();
        }
      })
    );
  }

  ngOnChanges(): void {
    this.showPageLoader = true;
    console.log(this.documentInfo, this.selectedPlanDocument)
  }

  onDocumentLoaded(): void {
    this.showPageLoader = false;
  }

  onPayClick() {
    if (this.showPaymentTemplate && this.isAgreementSubmitted && !(this.documentInfo?.isAgreementDone || this.documentInfo?.isPaid)) {
      this.onSubmit();
    } else if (
      this.showPaymentTemplate &&
      (!this.isAgreementSubmitted || this.documentInfo?.isAgreementDone) &&
      !this.documentInfo?.isPaid
    ) {
      //to be use  this.initPaymentProcess();
    } else {
      this.showPaymentTemplate = true;
    }
  }

  onSubmit(): void {
    this.showBtnLoader = true;
    this.signedDocumentService
      .add(
        {
          ...this.documentInfo,
          isAgreementDone: this.isAgreementDone,
          agreementDate: this.datePipe.transform(new Date(), this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss)
        },
        API_URL.crud.createOrEdit
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.initPaymentProcess();
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  initPaymentProcess(): void {
    this.showBtnLoader = true;
    setTimeout(() => {
      //tobe use
      // if (this.cardConnectPaymentComponent.isCardInvalid) {
      //   this.toasterService.error(this.constants.errorMessages.invalidCardDetails);
      //   this.showBtnLoader = false;
      //   // this.cardConnectPaymentComponent.resetTokenFrame();
      // } else {
      // this.cardConnectPaymentComponent.tokenize();
      // this.cardDetails = this.cardConnectPaymentComponent.cardDetails;
      // this.onConfirmationForPayment();
      // this.cdr.detectChanges();
      // }
      // this.cdr.detectChanges();
    }, 1000);
  }
  // use for recurring
  //onConfirmationForPayment(): void {
  // if(this.cardDetails.isUsingSavedCard === false) {
  // to be use for recurring payment
  // this.paymentService
  //   .add(this.getPaymentParams(), API_URL.payment.NMIPayment)
  //   .pipe(takeUntil(this.destroy$))
  //   .subscribe({
  //     next: () => {
  //       this.closeSideNavFun();
  //       this.refereshDocuments.emit();
  //       this.showBtnLoader = false;
  //       this.destroy$.subscribe(() => this.subscriptions.unsubscribe());
  //       this.toasterService.success(this.constants.successMessages.paymentSuccess);
  //       this.cdr.detectChanges();
  //     },
  //     error: () => {
  //       // this.cardConnectPaymentComponent.resetTokenFrame();
  //       this.isRePayment = true;
  //       this.isAgreementSubmitted = false;
  //       this.showBtnLoader = false;
  //       this.cdr.detectChanges();
  //     }
  //   });

  //} else {
  // to be use for recurring payment
  // this.paymentService
  //   .add(this.getCardDetails(), API_URL.payment.paymentUsingSavedCard)
  //   .pipe(takeUntil(this.destroy$))
  //   .subscribe({
  //     next: () => {
  //       this.toasterService.success(this.constants.successMessages.paymentSuccess);
  //       this.cdr.detectChanges();
  //     },
  //     error: () => {
  //       this.showBtnLoader = false;
  //       this.cdr.detectChanges();
  //     }
  //   });
  //}
  //}

  getCardDetails(): PaymentParams {
    return {
      userId: this.currentUser?.userId,
      dependentInformationId: this.documentInfo?.studentId,
      classType: ClassTypes.RECURRING,
      scheduleId: this.documentInfo?.recurringScheduleId,
      amount: this.documentInfo?.amount,
      paidDate: new Date(),
      customerVoultId: this.cardDetails.customerVaultId
    };
  }

  getPaymentParams(): PaymentParams {
    return {
      dependentInformationId: this.documentInfo?.studentId,
      classType: ClassTypes.RECURRING,
      scheduleId: this.documentInfo?.recurringScheduleId,
      amount: this.documentInfo?.amount,
      ccNum: this.cardDetails.number,
      ccExpiry: this.cardDetails.expiry,
      ccType: this.cardDetails.type,
      token: this.cardDetails.token,
      isSaveCard: this.cardDetails.isSaveCard,
      paidDate: new Date(),
      address: this.cardDetails.address,
      city: this.cardDetails.city,
      state: this.cardDetails.state,
      zip: this.cardDetails.zip,
      firstName: this.cardDetails.firstName,
      lastName: this.cardDetails.lastName
    };
  }

  closeSideNavFun(): void {
    this.closeSideNav.emit();
  }
}
