<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="back-btn-wrapper">
      <div class="ps-2">
        <div class="title">Assigned Successfully</div>
      </div>
    </div>
    <div class="action-btn-wrapper">
      <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button" (click)="closeBookAssignedPlan()">Done</button>
      <button
        *ngIf="selectedStudentPlan"
        mat-raised-button
        color="primary"
        class="mat-primary-btn"
        type="button"
        (click)="onViewSchedule()"
        [appLoader]="showBtnLoader"
      >
        View Schedule
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <div class="plan-success-wrapper">
      <img [src]="constants.staticImages.icons.checkCircle" height="70" width="70" alt="" />
      <div class="success">Assigned {{ selectedStudentPlan ? 'Plan ' : 'Product ' }} Successfully</div>
    </div>
    <div class="plan-details-wrapper">
      <ng-container [ngTemplateOutlet]="selectedStudentPlan ? planDetails : productDetails"></ng-container>
    </div>
  </div>
</div>

<ng-template #planDetails>
  <ng-container [ngTemplateOutlet]="!selectedEnsembleClass ? planDetail : ensemblePlan"></ng-container>
</ng-template>

<ng-template #planDetail>
  <div class="plan-details">
    <div class="title">Plan Details</div>
    <div class="plan-details-content">
      <div class="sub-title">Plan Name</div>
      <div class="plan-content">
        Weekly Music Lessons ({{ planSummaryService.getPlanType(selectedStudentPlan?.planType!) }}
        {{ planSummaryService.getPlanSummary(selectedStudentPlan?.plandetails?.items!) }})
      </div>
      <div class="space-between">
        <div>
          <div>
            <div class="sub-title">Class Type</div>
            <div class="plan-content">Recurring Plan</div>
          </div>
          <div>
            <div class="sub-title">Instrument</div>
            <div class="plan-content mb-0">{{ selectedInstrumentName }}</div>
          </div>
        </div>
        <div>
          <div>
            <div class="sub-title">Visit Per Week</div>
            <div class="plan-content">{{ (selectedStudentPlan?.plandetails?.items)![0].planDetail.visitsPerWeek }} visit</div>
          </div>
          <div>
            <div class="sub-title">Start From</div>
            <div class="plan-content mb-0">
              {{ bookPlanForm.getRawValue().scheduleDate | date : 'mediumDate' }} |
              {{ bookPlanForm.getRawValue().scheduleStartTime | date : 'shortTime' }} -
              {{ bookPlanForm.getRawValue().scheduleEndTime | date : 'shortTime' }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="dotted-divider"></div>
  <div class="plan-details">
    <div class="title">Document</div>
    <div class="plan-details-content">
      <div class="sub-title">Name</div>
      <div class="plan-content mb-0">{{ selectedStudentPlan?.documentName }}</div>
    </div>
  </div>
  <div class="dotted-divider"></div>
  <div class="plan-details">
    <div class="title">Payment</div>
    <div class="plan-details-content">
      <div class="space-between">
        <div>
          <div class="sub-title">Total Payable Amount</div>
          <div class="plan-content mb-0">${{ getTotalPayableAmount }}</div>
        </div>
        <div>
          <div class="sub-title">Payment Status</div>
          <div class="info pending">Pending</div>
        </div>
      </div>
    </div>
  </div>
  <div class="dotted-divider"></div>
  <div class="plan-details">
    <div class="title">Agreements</div>
    <div class="plan-details-content">
      <div class="space-between">
        <div>
          <div class="sub-title">Sent to</div>
          <div class="plan-content mb-0">
            {{ selectedStudentDetails?.firstName | titlecase }} {{ selectedStudentDetails?.lastName | titlecase }}
          </div>
        </div>
        <div>
          <div class="sub-title">Document Status</div>
          <div class="info primary-color">Agreed T&C Document</div>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #ensemblePlan>
  <div class="plan-details">
    <div class="title">Plan Details</div>
    <div class="plan-details-content">
      <div class="sub-title">Plan Name</div>
      <div class="plan-content">
        Weekly Music Lessons ({{ planSummaryService.getPlanType(selectedStudentPlan?.planType!) }}
        {{ planSummaryService.getPlanSummary(selectedStudentPlan?.plandetails?.items!) }})
      </div>
      <div class="space-between">
        <div>
          <div>
            <div class="sub-title">Class Type</div>
            <div class="plan-content">Ensemble Plan</div>
          </div>
          <div>
            <div class="sub-title">Instrument</div>
            <div class="plan-content mb-0">
              <ng-container *ngFor="let instrument of selectedEnsembleClass.assignedInstruments; let last = last">
                {{ instrument.instrumentName }}<span *ngIf="!last">, </span>
              </ng-container>
            </div>
          </div>
        </div>
        <div>
          <div>
            <div class="sub-title">Visit Per Week</div>
            <div class="plan-content">{{ (selectedStudentPlan?.plandetails?.items)![0].planDetail.visitsPerWeek }} visit</div>
          </div>
          <div>
            <div class="sub-title">Schedule Info</div>
            <div class="plan-content mb-0">
              {{ selectedEnsembleClass.scheduleStartDate | date : 'mediumDate' }} -
              {{ selectedEnsembleClass.scheduleEndDate | date : 'mediumDate' }} |
              {{ selectedEnsembleClass.scheduleStartTime | date : 'shortTime' }} -
              {{ selectedEnsembleClass.scheduleEndTime | date : 'shortTime' }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="dotted-divider"></div>
  <div class="plan-details">
    <div class="title">Document</div>
    <div class="plan-details-content">
      <div class="sub-title">Name</div>
      <div class="plan-content mb-0">{{ selectedStudentPlan?.documentName }}</div>
    </div>
  </div>
  <div class="dotted-divider"></div>
  <div class="plan-details">
    <div class="title">Payment</div>
    <div class="plan-details-content">
      <div class="space-between">
        <div>
          <div class="sub-title">Total Paid Amount</div>
          <div class="plan-content mb-0">${{ selectedStudentPlan?.planPrice }}</div>
        </div>
        <div>
          <div class="sub-title">Payment Status</div>
          <div class="info primary-color">Paid Successfully</div>
        </div>
      </div>
    </div>
  </div>
  <div class="dotted-divider"></div>
  <div class="plan-details">
    <div class="title">Agreements</div>
    <div class="plan-details-content">
      <div class="space-between">
        <div>
          <div class="sub-title">Sent to</div>
          <div class="plan-content mb-0">
            {{ selectedStudentDetails?.firstName | titlecase }} {{ selectedStudentDetails?.lastName | titlecase }}
          </div>
        </div>
        <div>
          <div class="sub-title">Document Status</div>
          <div class="info primary-color">Agreed T&C Document</div>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #productDetails>
  <div class="plan-details">
    <div class="title">Product Details</div>
    @for (product of shoppingCart; track $index) {
    <div class="plan-details-content">
      <div class="sub-title">Product Name</div>
      <div class="plan-content">{{ product.productName }}</div>
    </div>
    }
  </div>
  <div class="dotted-divider"></div>
  <div class="plan-details">
    <div class="title">Payment</div>
    <div class="plan-details-content">
      <div class="space-between">
        <div>
          <div class="sub-title">Total Paid Amount</div>
          <div class="plan-content mb-0">${{ totalAmount }}</div>
        </div>
        <div>
          <div class="sub-title">Payment Status</div>
          <div class="info primary-color">Paid Successfully</div>
        </div>
      </div>
    </div>
  </div>
</ng-template>
