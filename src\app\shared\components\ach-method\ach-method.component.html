<div class="payment-form">
  <div class="container">
    <form id="payment-form">
      <div class="form-row top-row">
        <div id="checkaccount" class="field full-width"></div>
        <div class="input-errors" id="ach-number-errors" role="alert"></div>
      </div>
      <div class="form-row">
        <div id="checkaba" class="field full-width"></div>
        <div class="input-errors" id="ach-routing-errors" role="alert"></div>
      </div>
      <div class="form-row">
        <div id="checkname" class="field full-width"></div>
        <div class="input-errors" id="ach-name-errors" role="alert"></div>
      </div>

      <app-address-form
        [selectedCardDetail]="selectedCardDetail"
        [savedAddress]="savedAddressDetails"
        (saveAddress)="onSaveAddress($event)"
      >
      </app-address-form>

      <div class="button-container">
        <button
          mat-raised-button
          id="payButton"
          color="primary"
          class="mat-primary-btn"
          [appLoader]="showBtnLoader"
          (click)="onAddAchMethod()"
          type="button"
        >
          Add
        </button>

        <button *ngIf="cardDetails?.length" mat-raised-button id="cancelButton" class="mat-accent-btn" (click)="cancel()" type="button">
          Cancel
        </button>
      </div>

      <div class="input-errors" id="pay-button-errors" role="alert"></div>
    </form>
  </div>
</div>
